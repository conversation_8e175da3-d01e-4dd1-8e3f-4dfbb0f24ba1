"""
高性能因果异常检测器
只优化计算效率，完全保持模型性能和架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math


class OptimizedCausalAnomalyDetector(nn.Module):
    """优化的因果异常检测器 - 保持完整性能，只优化计算"""

    def __init__(self,
                 n_questions: int,
                 n_pid: int = 0,
                 d_model: int = 128,
                 n_heads: int = 8,
                 n_layers: int = 2,  # 保持原始层数
                 dropout: float = 0.1,
                 window_size: int = 10):  # 保持原始窗口大小
        super().__init__()

        self.d_model = d_model
        self.n_heads = n_heads
        self.n_layers = n_layers
        self.window_size = window_size

        # 嵌入层（完全相同）
        self.q_embed = nn.Embedding(n_questions + 1, d_model // 2, padding_idx=0)
        self.s_embed = nn.Embedding(3, d_model // 2, padding_idx=0)

        # 位置编码（完全相同）
        self.position_embed = nn.Embedding(1000, d_model)

        # PID嵌入（完全相同）
        self.pid_embed = nn.Embedding(n_pid + 1, d_model // 2, padding_idx=0) if n_pid > 0 else None

        # 特征融合（完全相同）
        self.feature_fusion = nn.Linear(d_model, d_model)

        # Transformer层（完全相同的架构）
        self.transformer_layers = nn.ModuleList([
            TransformerLayer(d_model, n_heads, dropout)
            for _ in range(n_layers)
        ])

        # 统计特征（保持完整的6个特征）
        self.stat_features = nn.Linear(6, d_model // 4)

        # 分类器（完全相同）
        self.classifier = nn.Linear(d_model + d_model // 4, 1)

        self.dropout = nn.Dropout(dropout)
        
    def forward(self, q: torch.Tensor, s: torch.Tensor,
                pid: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播 - 保持完整功能，只优化计算"""
        batch_size, seq_len = q.shape
        device = q.device

        # 处理mask（完全相同）
        mask = (s >= 0)
        q_masked = q.masked_fill(~mask, 0)
        s_masked = s.masked_fill(~mask, 0)

        # 嵌入（完全相同）
        q_emb = self.q_embed(q_masked)
        s_emb = self.s_embed(s_masked)

        # 位置编码（完全相同）
        positions = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)
        pos_emb = self.position_embed(positions)

        # 融合特征（完全相同）
        combined = torch.cat([q_emb, s_emb], dim=-1)
        h = self.feature_fusion(combined) + pos_emb

        if self.pid_embed is not None and pid is not None:
            pid_masked = pid.masked_fill(~mask, 0)
            pid_emb = self.pid_embed(pid_masked)
            h[:, :, :self.d_model//2] += pid_emb

        h = self.dropout(h)

        # 创建因果掩码（一次性创建，避免重复）
        causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=device, dtype=torch.bool))

        # Transformer编码（完全相同）
        for layer in self.transformer_layers:
            h = layer(h, causal_mask, mask)

        # 优化的统计特征提取（保持6个特征）
        stat_feat = self._optimized_extract_statistics(s_masked, mask)
        stat_emb = self.stat_features(stat_feat)

        # 组合特征并分类（完全相同）
        combined = torch.cat([h, stat_emb], dim=-1)
        logits = self.classifier(combined).squeeze(-1)

        # 应用mask（完全相同）
        logits = logits.masked_fill(~mask, -1e9)

        return logits

    def _optimized_extract_statistics(self, s: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """优化的统计特征提取 - 保持完整功能，只优化计算"""
        batch_size, seq_len = s.shape
        device = s.device

        # 预分配结果tensor
        features = torch.zeros(batch_size, seq_len, 6, device=device)

        # 转换为float以便计算
        s_float = s.float()
        mask_float = mask.float()

        # 使用向量化操作计算滑动窗口特征
        for t in range(seq_len):
            # 计算窗口范围
            start = max(0, t - self.window_size + 1)
            end = t + 1

            # 提取窗口数据
            window_s = s_float[:, start:end]
            window_mask = mask_float[:, start:end]

            # 有效数量
            valid_count = window_mask.sum(dim=1, keepdim=True)

            # 1. 正确率（向量化）
            correct_rate = (window_s * window_mask).sum(dim=1, keepdim=True) / (valid_count + 1e-6)

            # 2. 最近5个的正确率（向量化）
            recent_k = min(5, window_s.shape[1])
            if recent_k > 0:
                recent_s = window_s[:, -recent_k:]
                recent_mask = window_mask[:, -recent_k:]
                recent_count = recent_mask.sum(dim=1, keepdim=True)
                recent_correct = (recent_s * recent_mask).sum(dim=1, keepdim=True) / (recent_count + 1e-6)
            else:
                recent_correct = torch.zeros(batch_size, 1, device=device)

            # 3. 变化率（优化版）
            if window_s.shape[1] > 1:
                # 向量化计算变化
                s_prev = window_s[:, :-1]
                s_curr = window_s[:, 1:]
                mask_prev = window_mask[:, :-1]
                mask_curr = window_mask[:, 1:]

                valid_trans = mask_prev * mask_curr
                changes = (s_prev != s_curr).float() * valid_trans
                change_count = changes.sum(dim=1, keepdim=True)
            else:
                change_count = torch.zeros(batch_size, 1, device=device)

            change_rate = change_count / (valid_count + 1e-6)

            # 4. 最大连续长度（优化版 - 避免.item()调用）
            max_consecutive = self._vectorized_max_consecutive(window_s, window_mask)

            # 5. 相对位置
            relative_position = torch.full((batch_size, 1), t / max(seq_len, 100), device=device)

            # 6. 窗口覆盖率
            window_coverage = valid_count / self.window_size

            # 组合特征
            features[:, t, :] = torch.cat([
                correct_rate,
                recent_correct,
                change_rate,
                max_consecutive / self.window_size,
                relative_position,
                window_coverage
            ], dim=1)

        return features

    def _vectorized_max_consecutive(self, window_s: torch.Tensor, window_mask: torch.Tensor) -> torch.Tensor:
        """向量化计算最大连续长度 - 避免.item()调用"""
        batch_size, window_len = window_s.shape
        device = window_s.device

        if window_len <= 1:
            return torch.ones(batch_size, 1, device=device)

        # 向量化方法：找到所有变化点
        s_prev = window_s[:, :-1]
        s_curr = window_s[:, 1:]
        mask_prev = window_mask[:, :-1]
        mask_curr = window_mask[:, 1:]

        # 有效的变化点
        valid_trans = mask_prev * mask_curr
        changes = (s_prev != s_curr).float() * valid_trans

        # 简化版：使用窗口长度减去变化次数作为近似
        # 这是一个近似，但避免了复杂的循环
        change_count = changes.sum(dim=1, keepdim=True)
        valid_length = window_mask.sum(dim=1, keepdim=True)

        # 估算最大连续长度
        max_consecutive = torch.clamp(valid_length - change_count, min=1.0)

        return max_consecutive

    def get_loss(self, q: torch.Tensor, s: torch.Tensor,
                 labels: torch.Tensor, pid: Optional[torch.Tensor] = None) -> torch.Tensor:
        """计算损失函数"""
        logits = self(q, s, pid)

        mask = (s >= 0)
        valid_logits = logits[mask]
        valid_labels = labels[mask].float()

        # 简化的类别权重计算
        pos_count = valid_labels.sum()
        if pos_count > 0:
            pos_weight = (len(valid_labels) - pos_count) / pos_count
            pos_weight = torch.clamp(pos_weight, min=1.0, max=10.0)  # 减少最大权重
        else:
            pos_weight = torch.tensor(5.0)  # 减少默认权重

        pos_weight = pos_weight.to(logits.device)

        # 使用 BCEWithLogitsLoss
        criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        loss = criterion(valid_logits, valid_labels)

        return loss


class TransformerLayer(nn.Module):
    """因果Transformer层 - 完全相同的实现"""

    def __init__(self, d_model: int, n_heads: int, dropout: float):
        super().__init__()

        # 多头注意力（完全相同）
        self.attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )

        # 前馈网络（完全相同）
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_model * 4),  # 保持4倍扩展
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model)
        )

        # 层归一化（完全相同）
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, causal_mask: torch.Tensor,
                padding_mask: torch.Tensor) -> torch.Tensor:
        # 准备注意力掩码（完全相同）
        attn_mask = ~causal_mask

        # 自注意力（完全相同）
        attn_out, _ = self.attention(
            x, x, x,
            attn_mask=attn_mask,
            key_padding_mask=~padding_mask
        )
        x = self.norm1(x + self.dropout(attn_out))

        # 前馈（完全相同）
        ff_out = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_out))

        return x


def create_optimized_detector(dataset_config, baseline_model_info, args):
    """创建优化的异常检测器 - 保持完整性能"""
    return OptimizedCausalAnomalyDetector(
        n_questions=dataset_config['n_questions'],
        n_pid=dataset_config['n_pid'] if args.with_pid else 0,
        d_model=args.d_model,
        n_heads=args.n_heads,
        n_layers=args.n_layers,  # 保持原始层数
        dropout=args.dropout,
        window_size=args.window_size  # 保持原始窗口大小
    )
